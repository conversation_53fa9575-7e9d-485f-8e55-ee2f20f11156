<template>
    <div class="safety-checks-container">
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid" funcMark="aqjcaqdjc" :customFunc="true" :params="params">
                <template slot="customHeadFunc" slot-scope="{ func }">
                    <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':aqjcaqdjc:add')"
                        @click.native="handleAddSafetyCheck('add')">安全大检查</Button>
                </template>
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button type="primary" style="margin-right: 10px;" v-if="func.includes(globalAppCode + ':aqjcaqdjc:detail')"
                        @click.native="handleDetail(index, row)">详情</Button>
                    <Button type="primary" v-if="func.includes(globalAppCode + ':aqjcaqdjc:dispose') && row.status == '01'"
                        @click.native="handleDispose(index, row)">处理</Button>
                    <Button type="primary" v-if="func.includes(globalAppCode + ':aqjcaqdjc:instructions') && row.status == '02'"
                        @click.native="handleInstructions(index, row)">领导批示</Button>

                </template>
            </s-DataGrid>
        </div>
        <div class="add-safety-check" v-if="addFromContainer">
            <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0">
                <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />安全大检查登记
                </p>

                <div class="add-form-header">
                    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" class="form-layout">
                        <!-- 第一行：检查部位（占两列） -->
                        <Row :gutter="16" class="form-row">
                            <Col span="12">
                                <FormItem label="检查部位" prop="checkRoomIdArr" class="wide-item">
                                    <Poptip trigger="click" placement="bottom-start" class="check-room-poptip" style="width: 100%;">
                                        <Input v-model="formValidate.checkRoomIdArr" placeholder="请选择检查部位" readonly style="width: 100%;" />
                                        <div slot="content" class="tree-content">
                                            <Tree ref="checkRoomTree" :key="treeKey" :data="checkRoomList" multiple show-checkbox
                                                @on-check-change="handleCheckChange" @getCheckedNodes="getCheckedNodes">
                                            </Tree>
                                        </div>
                                    </Poptip>
                                </FormItem>
                            </Col>
                        </Row>

                        <!-- 第二行：带队领导、参加民警、其他参加人 -->
                        <Row :gutter="16" class="form-row">
                            <Col span="12">
                                <FormItem label="带队领导" prop="leaderUserSfzh"
                                    :rules="{ required: false, message: '带队领导不能为空', trigger: 'blur, change' }">
                                    <user-selector v-model="formValidate.leaderUserSfzh" tit="用户选择"
                                        :text.sync="formValidate.leaderUserName" returnField="id" numExp='num >= 1'
                                        msg="至少选中1人">
                                    </user-selector>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="参加民警" prop="involvementUserSfzh"
                                    :rules="{ required: true, message: '参加民警不能为空', trigger: 'blur, change' }">
                                    <user-selector v-model="formValidate.involvementUserSfzh" tit="用户选择"
                                        :text.sync="formValidate.involvementUserName" returnField="id" numExp='num >= 1'
                                        msg="至少选中1人">
                                    </user-selector>
                                </FormItem>
                            </Col>
                        </Row>
                        <Row :gutter="16" class="form-row">
                            <Col span="12">
                                <FormItem label="检查时间" prop="checkTime"
                                    :rules="{ required: true, type: 'date', message: '请选择检查时间', trigger: 'change' }">
                                    <DatePicker type="datetime" v-model="formValidate.checkTime" :options="pickerOptions"
                                        format="yyyy-MM-dd HH:mm:ss" style="width: 100%;"
                                        :value="formValidate.checkTime" />
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="其他参加人" prop="otherParticipants"
                                    :rules="{ required: false, message: '请输入其他参加人', trigger: 'blur' }">
                                    <Input v-model="formValidate.otherParticipants" placeholder="请输入其他参加人" />
                                </FormItem>
                            </Col>
                        </Row>

                        <!-- 第三行：检查内容（独占一行） -->
                        <Row :gutter="16" class="form-row">
                            <Col span="24">
                                <FormItem label="检查内容" prop="checkItemIdArr" class="full-width-item check-content-item"
                                    :rules="{ required: true, type: 'array', message: '检查内容不能为空', trigger: 'blur' }">
                                    <cusSelect v-model="formValidate.checkItemIdArr" :options="cleanList"
                                        @handleSelectNameList="handleSelectNameList" style="width: 100%;" />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <!-- <header>
                <h3>检查情况</h3>
            </header> -->
                <p class="fm-content-wrap-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />检查情况
                </p>

                <div class="fm-content-wrap">
                    <Form ref="formValidate1" :model="formValidate1" class="form-layout">
                        <!-- 第一行：是否存在违禁、是否存在安全隐患 -->
                        <Row :gutter="16" class="form-row">
                            <Col span="24">
                                <FormItem label="是否存在违禁" prop="isViolation"
                                    :rules="{ required: true, type: 'number', message: '请选择是否存在违禁', trigger: 'change,blur' }">
                                    <RadioGroup v-model="formValidate1.isViolation">
                                        <Radio :label="1" disabled>是</Radio>
                                        <Radio :label="0" disabled>否</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>
                        </Row>
                        <Row :gutter="16" class="form-row">
                            <Col span="24">
                                <FormItem label="是否存在安全隐患" prop="isHiddenDanger"
                                    :rules="{ required: true, type: 'number', message: '请选择是否存在安全隐患', trigger: 'change,blur' }">
                                    <RadioGroup v-model="formValidate1.isHiddenDanger">
                                        <Radio :label="1" disabled>是</Radio>
                                        <Radio :label="0" disabled>否</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>
                        </Row>

                        <!-- 违禁情况相关字段 -->
                        <Row :gutter="16" class="form-row" v-if="notNormal == 'notNormal'">
                            <Col span="24">
                                <FormItem label="违禁情况" prop="violationContent" class="full-width-item"
                                    :rules="{ required: this.newArr.length >= 1 ? true : false, message: '请输入违禁情况', trigger: 'blur' }">
                                    <Input v-if="this.newArr.length == 1 || this.newArr.length == 0"
                                        v-model="formValidate1.violationContent" placeholder="请输入违禁情况" type="textarea" :rows="3" />
                                    <div v-else class="line-list-improved">
                                        <div v-for="item in newArr" :key="item" :title="item" class="line-item">{{ item }}</div>
                                    </div>
                                </FormItem>
                            </Col>
                        </Row>

                        <Row :gutter="16" class="form-row" v-if="notNormal == 'notNormal'">
                            <Col span="24">
                                <FormItem label="违禁情况附件" prop="violationAttachmentUrl" class="full-width-item">
                                    <file-upload :serviceMark="serviceMark" :bucketName="bucketName"
                                        @fileComplete="fileComplete" />
                                </FormItem>
                            </Col>
                        </Row>

                        <!-- 安全隐患情况相关字段 -->
                        <Row :gutter="16" class="form-row" v-if="notNormal == 'notNormal'">
                            <Col span="24">
                                <FormItem label="安全隐患情况" prop="hiddenDangerContent" class="full-width-item"
                                    :rules="{ required: this.newArr.length >= 1 ? true : false, message: '请输入安全隐患情况', trigger: 'blur' }">
                                    <Input v-if="this.newArr.length == 1 || this.newArr.length == 0"
                                        v-model="formValidate1.hiddenDangerContent" placeholder="请输入安全隐患情况" type="textarea" :rows="3" />
                                    <div v-else class="line-list-improved">
                                        <div v-for="item in newArr" :key="item" :title="item" class="line-item">{{ item }}</div>
                                    </div>
                                </FormItem>
                            </Col>
                        </Row>

                        <Row :gutter="16" class="form-row" v-if="notNormal == 'notNormal'">
                            <Col span="24">
                                <FormItem label="安全隐患情况附件" prop="hiddenDangerAttachmentUrl" class="full-width-item">
                                    <file-upload :serviceMark="serviceMark" :bucketName="bucketName"
                                        @fileComplete="fileComplete1" />
                                </FormItem>
                            </Col>
                        </Row>

                        <!-- 备注字段 -->
                        <Row :gutter="16" class="form-row">
                            <Col span="24">
                                <FormItem label="备注" prop="remarks" class="full-width-item">
                                    <Input type="textarea" v-model="formValidate1.remarks" placeholder="请输入备注" :rows="3" />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleCanecl" style="margin-right: 10px;">取消</Button>
                <Button type="primary" @click="handleSubmit">确认</Button>
            </div>
        </div>
        <div class="detail-containerlist" v-if="baseInfoContainer">
            <div class="fm-content-info">
                <Form ref="formData" :model="queryInfo" inline>
                    <div class="fm-content-box">
                        <p class="fm-content-info-title">
                            <Icon type="md-list-box" size="24" color="#2b5fda" />基本信息
                        </p>
                        <Row>
                            <Col span="3" class="col-title"><span>检查部位</span></Col>
                            <Col span="5"><span>{{ queryInfo.checkRoomId }}</span></Col>
                            <Col span="3"><span>带队领导</span></Col>
                            <Col span="5"><span>{{ queryInfo.leaderUserName }}</span></Col>
                            <Col span="3"><span>参加民警</span></Col>
                            <Col span="5"><span>{{ queryInfo.involvementUserName }}</span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>检查时间</span></Col>
                            <Col span="5"><span>{{ queryInfo.checkTime }}</span></Col>
                            <Col span="3"><span>登记人</span></Col>
                            <Col span="5"><span>{{ queryInfo.operatorXm }}</span></Col>
                            <Col span="3"><span>登记时间</span></Col>
                            <Col span="5"><span>{{ queryInfo.operatorTime }}</span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>检查内容</span></Col>
                            <Col span="21"><span>
                                <div v-for="item in queryInfo.checkContentList" :title="item">{{ item }}</div>
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>是否存在违禁</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.isViolation == 0 ? '否' : "是" }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>违禁情况</span></Col>
                            <Col span="21"><span>
                                <div v-for="item in queryInfo.violationContentList" :title="item">{{ item }}</div>
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>违禁情况附件</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.violationAttachmentUrl }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>是否存在安全隐患</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.isHiddenDanger == 0 ? '否' : "是" }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>安全隐患情况</span></Col>
                            <Col span="21"><span>
                                <div v-for="item in queryInfo.hiddenDangerContentList" :title="item">{{ item }}</div>
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>安全隐患附件</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.hiddenDangerAttachmentUrl }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>备注</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.remarks }}
                            </span></Col>
                        </Row>
                    </div>
                </Form>
            </div>
            <!-- 整改的form表单 -->
            <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0; margin: 0 26px 26px 26px"
                v-if="type == 'dispose'">
                <Form ref="formValidate3" :model="formValidate3" :label-width="150" style="width: 100%;">
                    <div class="fm-content-form">
                        <p class="fm-content-wrap-title">
                            <Icon type="md-list-box" size="24" color="#2b5fda" />整改措施
                        </p>
                        <Row style="padding: 10px;">
                            <Col span="12">
                            <FormItem label="整改时间" prop="rectificationrTime"
                                :rules="{ required: true, type: 'date', message: '请选择整改时间', trigger: 'change' }">
                                <DatePicker type="date" format="yyyy-MM-dd" placeholder="请选择日期"
                                    v-model="formValidate3.rectificationrTime"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="12">
                            <FormItem label="整改情况" prop="rectificationrContent"
                                :rules="{ required: true, message: '请输入整改情况', trigger: 'blur' }">
                                <Input v-model="formValidate3.rectificationrContent" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入整改情况	"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </div>
            <!-- 整改详情信息 -->
            <div class="fm-content-info" v-if="type == 'instructions' || type == 'detail'">
                <Form ref="formData" :model="queryInfo" inline>
                    <div class="fm-content-box">
                        <p class="fm-content-info-title">
                            <Icon type="md-list-box" size="24" color="#2b5fda" />整改措施
                        </p>
                        <Row>
                            <Col span="3" class="col-title"><span>整改情况</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.rectificationrContent }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>整改时间</span></Col>
                            <Col span="9"><span>
                                {{ queryInfo.rectificationrTime }}
                            </span>
                            </Col>
                            <Col span="3" class="col-title"><span>整改人</span></Col>
                            <Col span="9"><span>
                                {{ queryInfo.rectificationrOperXm }}
                            </span></Col>
                        </Row>

                    </div>
                </Form>
            </div>
            <!-- 领导批示表单 -->
            <div class="fm-content-wrap" style="border-top:1px solid #cee0f0 ;padding: 0; margin: 0 26px 26px 26px"
                v-if="type == 'instructions'">
                <Form ref="formValidate4" :model="formValidate4" :label-width="150" style="width: 100%;">
                    <div class="fm-content-form">
                        <p class="fm-content-wrap-title">
                            <Icon type="md-list-box" size="24" color="#2b5fda" />整改措施
                        </p>
                        <Row style="padding: 10px;">
                            <Col span="24">
                            <FormItem label="领导批示" prop="leaderApprovalComments"
                                :rules="{ required: true, message: '请输入领导批示说明', trigger: 'blur' }">
                                <Input v-model="formValidate4.leaderApprovalComments" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入领导批示说明	"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                    </div>
                </Form>
            </div>
            <!-- 领导批示详情 -->
            <div class="fm-content-info" v-if="type == 'detail'">
                <Form ref="formData" :model="queryInfo" inline>
                    <div class="fm-content-box">
                        <p class="fm-content-info-title">
                            <Icon type="md-list-box" size="24" color="#2b5fda" />领导批示
                        </p>
                        <Row>
                            <Col span="3" class="col-title"><span>领导批示</span></Col>
                            <Col span="21"><span>
                                {{ queryInfo.leaderApprovalComments }}
                            </span></Col>
                        </Row>
                        <Row>
                            <Col span="3" class="col-title"><span>批示领导</span></Col>
                            <Col span="9"><span>
                                {{ queryInfo.leaderApproverXm }}
                            </span>
                            </Col>
                            <Col span="3" class="col-title"><span>批示时间</span></Col>
                            <Col span="9"><span>
                                {{ queryInfo.leaderApproverTime }}
                            </span></Col>
                        </Row>

                    </div>
                </Form>
            </div>
            <div class="bsp-base-fotter" v-if="type == 'dispose' || type == 'instructions'">
                <Button @click="handleCaneclBaseInfo" style="margin-right: 10px;">取消</Button>
                <Button type="primary" @click="handleSubmitBaseInfo">确认</Button>
            </div>
            <div class="bsp-base-fotter" v-else>
                <Button @click="handleCaneclBaseInfo" style="margin-right: 10px;">取消</Button>
            </div>
        </div>
    </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import { userSelector } from 'sd-user-selector'
import { fileUpload } from 'sd-minio-upfile'
import cusSelect from '../components/cusSelect.vue'
export default {
    name: "checkconfiguration",
    data() {
        return {
            tableContainer: true,
            addFromContainer: false,
            baseInfoContainer: false,
            params: {},
            formValidate: {
                checkRoomIdArr: "",
                checkRoomId: "",
                selectedValue: "",
                leaderUserSfzh: "",
                leaderUserName: "",
                involvementUserSfzh: "",
                involvementUserName: "",
                checkTime: new Date(),
                checkContent: "",
                checkType: 2,
                dataSources: "1",
                checkItemIdArr: [],
                checkItemId: "",
                violationAttachmentUrl: "",
                hiddenDangerAttachmentUrl: "",
                otherParticipants: ""
            },
            formValidate1: {
                isViolation: 0,
                violationContent: "",
                isHiddenDanger: 0,
                hiddenDangerContent: "",
                remarks: "",
            },
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            ruleValidate: {},
            cleanList: [],
            checkRoomList: [],
            defaultList: [],
            defaultList1: [],
            id: "",
            treeKey: 0, // 用于强制重新渲染Tree组件
            pickerOptions: {
                disabledDate(date) {
                    // 禁用今天之后的所有日期
                    return date.valueOf() > new Date().valueOf();
                }
            },
            newArr: [],
            notNormal: "",
            selectedIds: [],
            queryInfo: {
                violationContentList: [],
                checkContentList: [],
                hiddenDangerContentList: []
            },
            type: "",
            formValidate3: {
                rectificationrTime: new Date(), // 初始化为当前日期,
                rectificationrContent: "",
            },
            formValidate4: {
                leaderApprovalComments: "",
            },
        }
    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleAddSafetyCheck() {
            this.tableContainer = false
            this.addFromContainer = true

            // 初始化表单数据，确保所有状态都被重置
            this.initializeForm()

            // 打开新增表单时加载检查部位数据
            this.getList()
        },

        // 重置树组件选中状态的方法
        resetTreeSelection() {
            // 通过改变key值来强制重新渲染Tree组件，这样可以完全重置选中状态
            this.treeKey += 1;
        },

        // 初始化表单数据的方法
        initializeForm() {
            this.formValidate = {
                checkRoomIdArr: "",
                checkRoomId: "",
                selectedValue: "",
                leaderUserSfzh: "",
                leaderUserName: "",
                involvementUserSfzh: "",
                involvementUserName: "",
                checkTime: new Date(),
                checkContent: "",
                checkType: 2,
                dataSources: "1",
                checkItemIdArr: [],
                checkItemId: "",
                violationAttachmentUrl: "",
                hiddenDangerAttachmentUrl: "",
                otherParticipants: ""
            };

            this.formValidate1 = {
                isViolation: 0,
                violationContent: "",
                isHiddenDanger: 0,
                hiddenDangerContent: "",
                remarks: "",
            };

            this.newArr = [];
            this.notNormal = "";

            // 重置表单验证状态和树组件选中状态
            this.$nextTick(() => {
                if (this.$refs['formValidate']) {
                    this.$refs['formValidate'].resetFields();
                }
                if (this.$refs['formValidate1']) {
                    this.$refs['formValidate1'].resetFields();
                }
                // 重置树组件的选中状态
                this.resetTreeSelection();
            });
        },

        handleSelectNameList(item) {
            this.newArr = item.filter(item => item.includes('异常'))
            if (this.newArr.length >= 1) {
                this.formValidate1.hiddenDangerContent = this.newArr.join(',')
                this.formValidate1.violationContent = this.newArr.join(',')
                this.formValidate1.isViolation = 1
                this.formValidate1.isHiddenDanger = 1
                this.notNormal = 'notNormal'
            } else {
                this.formValidate1.isViolation = 0
                this.formValidate1.isHiddenDanger = 0
                this.notNormal = ''
            }
            this.formValidate.checkContent = item.join(',')
        },
        formatDate(date) {
            // 将Date对象格式化为"yyyy-MM-dd HH:mm:ss"格式
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        handleGetList() {
            this.authPostRequest({
                url: this.$path.cleanConfig_list, params: {
                    "checkType": "2", // 安全大检查使用checkType=2
                    "orgCode": ""
                }
            }).then(res => {
                if (res.success) {
                    let list = res.data;
                    this.cleanList = list.map(item => {
                        return {
                            label: item.checkItem,
                            value: item.id,
                            status: null,
                        }
                    })
                } else {
                    console.error('获取检查内容失败:', res.message);
                }
            }).catch(err => {
                console.error('获取检查内容API调用失败:', err);
            })
        },
        fileComplete(data) {
            this.$set(this.formValidate1, 'violationAttachmentUrl', data && data.length > 0 ? JSON.stringify(data) : '')
        },
        fileComplete1(data) {
            this.$set(this.formValidate1, 'hiddenDangerAttachmentUrl', data && data.length > 0 ? JSON.stringify(data) : '')
        },
        // 获取列表
        getList() {
            let orgCode = this.$store.state.common.orgCode
            this.authGetRequest({ url: this.$path.bsp_system_getAreaTree, params: { areaName: "", orgCode: orgCode } }).then(resp => {
                if (resp.success && resp.data) {
                    let areaList = this.transformData(resp.data)
                    this.checkRoomList = areaList
                } else {
                    this.errorModal({ content: '数据请求失败，请联系管理员' })
                }
            })
        },
        transformData(data) {
            return data.map((item) => {
                const newItem = {
                    ...item, // 保留原始所有字段
                    value: item.id, // 新增value字段
                    title: item.name // 新增label字段
                }

                if (item.children && item.children.length > 0) {
                    newItem.children = this.transformData(item.children) // 递归处理子节点
                }

                return newItem
            })
        },

        handleCheckChange(nodes) {
            // 过滤出叶子节点（没有子节点的节点）
            const leafNodes = nodes.filter(node => !node.children || node.children.length === 0);

            // 获取选中的叶子节点名称和ID
            const names = leafNodes.map(node => node.name);
            const ids = leafNodes.map(node => node.id);

            // 更新表单数据
            this.formValidate.checkRoomId = ids.join(',');
            this.formValidate.checkRoomIdArr = names.join(", "); // 回显到输入框

            // 如果没有选中任何节点，清空输入框
            if (nodes.length === 0) {
                this.formValidate.checkRoomId = '';
                this.formValidate.checkRoomIdArr = '';
            }
        },
        getCheckedNodes() {
            // 获取选中的节点，可以在这里添加额外的处理逻辑
        },
        // 处理
        handleDispose(index, { id }) {
            this.type = 'dispose'
            this.id = id
            this.tableContainer = false
            this.baseInfoContainer = true
            this.handleGetClean(id)
        },
        // 领导批示
        handleInstructions(index, { id }) {
            this.type = 'instructions'
            this.id = id
            this.tableContainer = false
            this.baseInfoContainer = true
            this.handleGetClean(id)
        },
        // 详情
        handleDetail(index, { id }) {
            this.type = "detail"
            this.id = id
            this.tableContainer = false
            this.baseInfoContainer = true
            this.handleGetClean(id)
        },
        handleCaneclBaseInfo() {
            this.tableContainer = true
            this.baseInfoContainer = false
        },
        handleGetClean(id) {
            this.authGetRequest({ url: this.$path.clean_get, params: { id } }).then(res => {
                if (res.success) {
                    this.queryInfo = res.data
                    this.queryInfo.violationContentList = res.data.violationContent.split(',')
                    this.queryInfo.checkContentList = res.data.checkContent.split(',')
                    this.queryInfo.hiddenDangerContentList = res.data.hiddenDangerContent.split(',')
                }
            })
        },
        handleSubmitBaseInfo() {
            let paramsId = {
                id: this.id
            }
            if (this.type == 'dispose') {
                this.$refs['formValidate3'].validate((valid) => {
                    if (valid) {
                        this.authPostRequest({ url: this.$path.clean_policeRectification, params: { ...paramsId, ...this.formValidate3 } }).then(res => {
                            if (res.success) {
                                this.$Message.success('整改成功')
                                this.tableContainer = true
                                this.baseInfoContainer = false
                                this.formValidate3 = {}
                            } else {
                                this.$Message.error(res.message)
                            }
                        })
                    } else {
                        this.$Message.error('验证未通过');
                    }
                })
            } else {
                this.$refs['formValidate4'].validate((valid) => {
                    if (valid) {
                        this.authPostRequest({ url: this.$path.clean_leaderInstruction, params: { ...paramsId, ...this.formValidate4 } }).then(res => {
                            if (res.success) {
                                this.$Message.success('操作成功')
                                this.tableContainer = true
                                this.baseInfoContainer = false
                                this.formValidate4 = {}
                            } else {
                                this.$Message.error(res.message)
                            }
                        })
                    } else {
                        this.$Message.error('验证未通过');
                    }
                })
            }
        },

        handleCanecl() {
            this.tableContainer = true
            this.addFromContainer = false

            // 使用统一的初始化方法
            this.initializeForm()
        },
        handleSubmit() {
            this.$nextTick(() => {
                Promise.all([this.$refs['formValidate'].validate(), this.$refs['formValidate1'].validate()]).then(res => {
                    if (res.every(item => item)) {
                        // 验证检查部位是否已选择
                        this.formValidate.checkTime = this.formatDate(this.formValidate.checkTime)
                        if (this.formValidate.checkRoomIdArr && this.formValidate.checkRoomIdArr.trim() !== '') {
                            this.formValidate.checkItemId = this.formValidate.checkItemIdArr.map(item => { return item.value }).join(',')
                            let params = { ...this.formValidate, ...this.formValidate1 }
                            this.authPostRequest({ url: this.$path.clean_create, params }).then(res => {
                                if (res.success) {
                                    // 使用统一的初始化方法重置表单
                                    this.initializeForm()
                                    this.tableContainer = true
                                    this.addFromContainer = false
                                    this.$Message.success('安全大检查登记成功')
                                    // 延迟刷新表格，确保组件完全初始化
                                    this.$nextTick(() => {
                                        setTimeout(() => {
                                            this.on_refresh_table()
                                        }, 100)
                                    })

                                } else {
                                    this.$Message.error(res.message)
                                }
                            })
                        } else {
                            this.$Message.error('请选择检查部位')
                        }

                    } else {
                        this.$Message.error('验证不通过')
                    }
                })
            })
        },
        on_refresh_table() {
            if (this.$refs.grid && this.$refs.grid.query_grid_data && this.$refs.grid.comModel) {
                this.$refs.grid.query_grid_data(1)
            } else {
                console.warn('DataGrid not fully initialized yet, skipping refresh')
            }
        }

    },

    components: {
        sDataGrid,
        userSelector,
        fileUpload,
        cusSelect,
    },

    created() {
        this.handleGetList()
    },
    computed: {
        getTableGrid() {
            console.error(this.$store.state.common.getTableGrid);
            return this.$store.state.common.getTableGrid
        }
    },
    watch: {
        'this.getTableGrid': {
            handler(newVa) {
                console.error(newVa);

            }
        }
    },



}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

// 限制作用域到当前组件
.safety-checks-container /deep/.fm-content-wrap-title {
    border-bottom: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 16px;
    color: #00244A;
    /* margin-bottom: 16px; */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #CEE0F0;
}

.safety-checks-container {
    .add-safety-check {
        width: 100%;
        height: 100%;

        // background-color: #fff;

        header {
            height: 50px;
            line-height: 50px;
            padding: 0 20px;
            background-color: #fff;
            border-bottom: solid 1px #f5f5f5;
        }

        .add-form-header {
            background-color: #fff;
            padding: 24px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        // 设置 fm-content-wrap 最小高度，确保内容能够完整显示
        .fm-content-wrap {
            min-height: 500px !important;
            height: auto !important;
            overflow: visible !important;
        }

        // 表单布局优化样式
        .form-layout {
            .form-row {
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            // 统一表单项高度和间距
            /deep/.ivu-form-item {
                margin-bottom: 0;

                .ivu-form-item-label {
                    width: 200px !important;
                    font-weight: 500;
                    color: #333;
                    text-align: right;
                    padding-right: 12px;
                    line-height: 32px;
                }

                .ivu-form-item-content {
                    line-height: 32px;
                }
            }

            // 全宽度字段样式
            .full-width-item {
                /deep/.ivu-form-item-content {
                    .ivu-input,
                    .check-room-poptip {
                        width: 100% !important;
                    }
                }
            }

            // 检查内容字段特殊样式 - 确保label宽度一致
            .check-content-item {
                /deep/.ivu-form-item-label {
                    width: 200px !important;
                    font-weight: 500;
                    color: #333;
                    text-align: right;
                    padding-right: 12px;
                    line-height: 32px;
                }

                /deep/.ivu-form-item-content {
                    width: 95%;
                    line-height: 32px;
                }
            }

            // 统一输入框高度
            /deep/.ivu-input,
            /deep/.ivu-date-picker,
            /deep/.ivu-select {
                height: 32px;

                .ivu-input-inner,
                .ivu-date-picker-rel input,
                .ivu-select-selection {
                    height: 32px;
                    line-height: 30px;
                }
            }

            // 用户选择器样式优化
            /deep/.user-selector {
                .ivu-btn {
                    height: 32px;
                    line-height: 30px;
                }
            }

            // 响应式布局优化
            @media (max-width: 1200px) {
                .form-row {
                    .ivu-col {
                        margin-bottom: 16px;
                    }
                }
            }

            // 必填字段标识优化
            /deep/.ivu-form-item-required {
                .ivu-form-item-label:before {
                    content: '*';
                    color: #ed4014;
                    margin-right: 4px;
                }
            }
        }

        .add-form-footer {
            max-height: 500px;
            overflow: auto;
            background-color: #fff;
            padding: 20px 0 10px 15px;
        }
    }
}

// 限制 el-input 样式作用域到当前组件
.safety-checks-container /deep/.el-input__inner {
    -webkit-appearance: none;
    appearance: none;
    background-color: #FFF;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    height: 32px;
    line-height: 32px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    width: 100%;
}

.safety-checks-container /deep/.el-input__icon {
    height: 100%;
    width: 25px;
    text-align: center;
    -webkit-transition: all .3s;
    transition: all .3s;
    line-height: 32px;
}

// 限制作用域，避免影响其他模块
.safety-checks-container .fm-content-wrap-title {
    border-bottom: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN;
    font-weight: 700;
    font-size: 16px;
    color: #00244a;
    margin-bottom: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

/* 检查部位下拉框样式 */
.check-room-poptip {
    width: 100% !important;
    display: block !important;
}

/* 确保检查部位输入框宽度100% */
.check-room-poptip .ivu-input {
    width: 100% !important;
}

.check-room-poptip /deep/.ivu-input {
    width: 100% !important;
}
.check-room-poptip /deep/.ivu-poptip-rel{
       width: 100% !important;
    }

/* 覆盖全局的 Poptip 样式，确保正常显示 */
.check-room-poptip /deep/.ivu-poptip-popper {
    min-width: 600px;
    max-width: 800px;
    min-height: 300px;
    max-height: 600px;
    overflow-y: auto;
    z-index: 1000;
    background: #fff !important;
    border: 1px solid #dcdee2 !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.check-room-poptip /deep/.ivu-poptip-inner {
    background-color: #fff !important;
    border: none !important;
    border-radius: 4px !important;
    box-shadow: none !important;
    min-height: 200px !important;
    height: auto !important;
}

.check-room-poptip /deep/.ivu-poptip-body {
    padding: 0 !important;
    min-height: 200px !important;
    height: auto !important;
}

.check-room-poptip /deep/.ivu-poptip-body-content-inner {
    color: #515a6e !important;
    padding: 0 !important;
    min-height: 200px !important;
    height: auto !important;
}

/* 修复箭头样式 */
.check-room-poptip /deep/.ivu-poptip-popper[x-placement^="bottom"] .ivu-poptip-arrow:after {
    border-bottom-color: #fff !important;
}

.check-room-poptip /deep/.ivu-poptip-popper[x-placement^="top"] .ivu-poptip-arrow:after {
    border-top-color: #fff !important;
}

.check-room-poptip /deep/.ivu-poptip-popper[x-placement^="left"] .ivu-poptip-arrow:after {
    border-left-color: #fff !important;
}

.check-room-poptip /deep/.ivu-poptip-popper[x-placement^="right"] .ivu-poptip-arrow:after {
    border-right-color: #fff !important;
}

/* 全局强制样式 - 确保样式生效 */
div.fm-content-wrap {
    min-height: 500px !important;
    height: auto !important;
    overflow: visible !important;
}

/* 检查部位全局强制样式 */
.check-room-poptip,
div.check-room-poptip {
    width: 100% !important;
    display: block !important;
}

.check-room-poptip input,
.check-room-poptip .ivu-input,
div.check-room-poptip input,
div.check-room-poptip .ivu-input {
    width: 100% !important;
}

.tree-content {
    padding: 10px;
    min-height: 80px !important;
    max-height: 350px !important;
    height: auto !important;
    overflow-y: auto;
    background: #fff;
    color: #515a6e;
}

.tree-content /deep/.ivu-tree {
    font-size: 14px;
    color: #515a6e !important;
    min-height: 60px !important;
    height: auto !important;
}

.tree-content /deep/.ivu-tree-children {
    height: auto !important;
}

.tree-content /deep/.ivu-tree li {
    height: auto !important;
    min-height: 24px !important;
    line-height: 24px !important;
}

.tree-content /deep/.ivu-tree-title {
    padding: 2px 4px;
    border-radius: 3px;
    color: #515a6e !important;
}

.tree-content /deep/.ivu-tree-title:hover {
    background-color: #f5f7fa !important;
    color: #2d8cf0 !important;
}

.tree-content /deep/.ivu-checkbox-wrapper {
    color: #515a6e !important;
}

.tree-content /deep/.ivu-checkbox-wrapper:hover {
    color: #2d8cf0 !important;
}

/* 优化树节点展开/收起图标样式 */
.tree-content /deep/.ivu-tree-arrow {
    color: #515a6e !important;
}

.tree-content /deep/.ivu-tree-arrow:hover {
    color: #2d8cf0 !important;
}

/* 优化选中状态的样式 */
.tree-content /deep/.ivu-tree-title-selected {
    background-color: #ebf1ff !important;
    color: #2d8cf0 !important;
}

/* 确保复选框正常显示 */
.tree-content /deep/.ivu-checkbox-inner {
    background-color: #fff !important;
    border-color: #dcdee2 !important;
}

.tree-content /deep/.ivu-checkbox-checked .ivu-checkbox-inner {
    background-color: #2d8cf0 !important;
    border-color: #2d8cf0 !important;
}

// 限制作用域，避免影响其他模块
.safety-checks-container .line-list {
    padding: 4px 7px;
    font-size: 14px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;

    div {
        width: 250px;
        /* 固定宽度（必选） */
        white-space: nowrap;
        /* 禁止换行 */
        overflow: hidden;
        /* 隐藏溢出内容 */
        text-overflow: ellipsis;
        /* 溢出时显示省略号 */
    }
}

// 改进的line-list样式，解决文本换行问题
.safety-checks-container .line-list-improved {
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    color: #515a6e;
    background-color: #fff;
    min-height: 32px;
    line-height: 1.5;

    .line-item {
        display: inline-block;
        margin: 2px 8px 2px 0;
        padding: 2px 8px;
        background-color: #f5f7fa;
        border-radius: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
        vertical-align: top;
    }
}
</style>
